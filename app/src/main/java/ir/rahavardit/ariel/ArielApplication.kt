package ir.rahavardit.ariel

import android.app.Application
import androidx.core.text.TextUtilsCompat
import androidx.core.view.ViewCompat
import ir.rahavardit.ariel.data.api.RetrofitClient
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.BuildConfig
import android.util.Log
import java.util.Locale

/**
 * Application class for initializing app-wide components.
 */
class ArielApplication : Application() {

    lateinit var sessionManager: SessionManager
        private set

    override fun onCreate() {
        super.onCreate()

        // Force RTL layout direction
        forceRtlIfSupported()

        // initialize session manager
        sessionManager = SessionManager(this)

        // Use the build flavor's BASE_URL
        RetrofitClient.setBaseUrl(BuildConfig.BASE_URL)

        // Log.d("ArielApplication", "Using API base URL: ${BuildConfig.BASE_URL}")
    }

    private fun forceRtlIfSupported() {
        // Force RTL layout direction for the entire app
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.JELLY_BEAN_MR1) {
            // Set the default locale to a RTL locale (Persian)
            val locale = Locale("fa", "IR")
            Locale.setDefault(locale)

            val config = resources.configuration
            config.setLocale(locale)
            config.setLayoutDirection(locale)

            // Apply the configuration
            resources.updateConfiguration(config, resources.displayMetrics)
        }
    }
}
