package ir.rahavardit.ariel.ui.login

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import ir.rahavardit.ariel.MainActivity
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.databinding.ActivityLoginBinding
import ir.rahavardit.ariel.utils.NetworkUtils
import ir.rahavardit.ariel.utils.BiometricAuthManager
import com.google.android.material.dialog.MaterialAlertDialogBuilder

/**
 * Activity for handling user login.
 */
class LoginActivity : AppCompatActivity() {

    private lateinit var binding: ActivityLoginBinding
    private lateinit var viewModel: LoginViewModel
    private lateinit var sessionManager: SessionManager
    private lateinit var biometricAuthManager: BiometricAuthManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)

        viewModel = ViewModelProvider(this)[LoginViewModel::class.java]
        sessionManager = SessionManager(this)
        biometricAuthManager = BiometricAuthManager(this)

        // Check internet connection first
        if (!NetworkUtils.isInternetAvailable(this)) {
            showNoInternetDialog()
            return
        }

        // Check if user is already logged in
        if (sessionManager.isLoggedIn()) {
            navigateToMainActivity()
            return
        }

        setupListeners()
        observeViewModel()
        setupBiometricLogin()
    }

    /**
     * Sets up click listeners and other UI interactions.
     */
    private fun setupListeners() {
        binding.btnLogin.setOnClickListener {
            val username = binding.etUsername.text.toString().trim()
            val password = binding.etPassword.text.toString().trim()

            val (isUsernameValid, isPasswordValid) = viewModel.validateInputs(username, password)

            when {
                !isUsernameValid -> {
                    binding.tilUsername.error = getString(R.string.login__please_enter_username)
                }
                !isPasswordValid -> {
                    binding.tilPassword.error = getString(R.string.login__please_enter_password)
                }
                else -> {
                    // Clear any previous errors
                    binding.tilUsername.error = null
                    binding.tilPassword.error = null

                    // Attempt login
                    viewModel.login(username, password)
                }
            }
        }

        binding.btnBiometricLogin.setOnClickListener {
            performBiometricLogin()
        }

        // Clear errors when text changes
        binding.etUsername.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) binding.tilUsername.error = null
        }

        binding.etPassword.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) binding.tilPassword.error = null
        }
    }

    /**
     * Observes LiveData from the ViewModel.
     */
    private fun observeViewModel() {
        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.cardLogin.visibility = if (isLoading) View.INVISIBLE else View.VISIBLE
        }

        viewModel.loginResult.observe(this) { result ->
            when (result) {
                is LoginViewModel.LoginResult.Success -> {
                    Toast.makeText(this, R.string.login__login_successful, Toast.LENGTH_SHORT).show()

                    // Save user session data
                    sessionManager.saveAuthToken(result.loginResponse.token)
                    sessionManager.saveUser(result.user)
                    sessionManager.setLoggedIn(true)

                    // Save credentials for biometric login if biometric is available
                    if (biometricAuthManager.checkBiometricAvailability() == BiometricAuthManager.BiometricAvailability.AVAILABLE) {
                        val username = binding.etUsername.text.toString().trim()
                        val password = binding.etPassword.text.toString().trim()
                        if (username.isNotEmpty() && password.isNotEmpty()) {
                            sessionManager.saveBiometricCredentials(username, password)
                            sessionManager.setBiometricEnabled(true)
                            android.util.Log.d("BiometricLogin", "Saved biometric credentials for user: $username")
                        }
                    }

                    // Navigate to main activity
                    navigateToMainActivity()
                }
                is LoginViewModel.LoginResult.Error -> {
                    Toast.makeText(this, result.errorMessage, Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    /**
     * Navigates to the MainActivity and finishes the current activity.
     */
    private fun navigateToMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish()
    }

    /**
     * Shows a dialog when there's no internet connection.
     */
    private fun showNoInternetDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_no_internet, null)
        val retryButton = dialogView.findViewById<Button>(R.id.btn_retry)

        val dialog = MaterialAlertDialogBuilder(this)
            .setView(dialogView)
            .setCancelable(false)
            .create()

        retryButton.setOnClickListener {
            dialog.dismiss()
            // Check internet connection again
            if (NetworkUtils.isInternetAvailable(this)) {
                // Internet is now available, restart the activity to proceed with normal flow
                recreate()
            } else {
                // Still no internet, show dialog again
                showNoInternetDialog()
            }
        }

        dialog.show()
    }

    /**
     * Sets up biometric login functionality.
     */
    private fun setupBiometricLogin() {
        // Check if biometric authentication is available and user has enabled it
        val availability = biometricAuthManager.checkBiometricAvailability()
        val isBiometricEnabled = sessionManager.isBiometricEnabled()
        val hasCredentials = sessionManager.getBiometricCredentials() != null

        // Debug logging
        android.util.Log.d("BiometricLogin", "Availability: $availability")
        android.util.Log.d("BiometricLogin", "Biometric enabled: $isBiometricEnabled")
        android.util.Log.d("BiometricLogin", "Has credentials: $hasCredentials")

        // Show biometric button if biometric is available AND we have saved credentials
        if (availability == BiometricAuthManager.BiometricAvailability.AVAILABLE && hasCredentials) {
            binding.btnBiometricLogin.visibility = View.VISIBLE
            android.util.Log.d("BiometricLogin", "Showing biometric button")
        } else {
            binding.btnBiometricLogin.visibility = View.GONE
            android.util.Log.d("BiometricLogin", "Hiding biometric button - availability: $availability, hasCredentials: $hasCredentials")
        }
    }

    /**
     * Performs biometric authentication and logs in the user.
     */
    private fun performBiometricLogin() {
        val credentials = sessionManager.getBiometricCredentials()

        if (credentials == null) {
            // No saved credentials - show message asking user to login normally first
            Toast.makeText(this, "لطفاً ابتدا با نام کاربری و رمز عبور وارد شوید", Toast.LENGTH_LONG).show()
            return
        }

        biometricAuthManager.authenticate(
            title = getString(R.string.biometric_login_title),
            subtitle = getString(R.string.biometric_login_subtitle),
            callback = object : BiometricAuthManager.BiometricAuthCallback {
                override fun onAuthenticationSucceeded() {
                    // Use saved credentials to login
                    val (username, password) = credentials
                    binding.etUsername.setText(username)
                    binding.etPassword.setText(password)
                    viewModel.login(username, password)
                }

                override fun onAuthenticationError(errorMessage: String) {
                    Toast.makeText(this@LoginActivity, errorMessage, Toast.LENGTH_LONG).show()
                }
            }
        )
    }
}
