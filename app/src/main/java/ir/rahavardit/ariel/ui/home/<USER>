package ir.rahavardit.ariel.ui.home

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import ir.rahavardit.ariel.ArielApplication
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.data.model.TicketBox
import ir.rahavardit.ariel.data.model.TicketBoxType
import ir.rahavardit.ariel.databinding.FragmentHomeBinding

class HomeFragment : Fragment() {

    private var _binding: FragmentHomeBinding? = null
    private lateinit var homeViewModel: HomeViewModel
    private lateinit var ticketBoxAdapter: TicketBoxAdapter

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val sessionManager = (requireActivity().application as ArielApplication).sessionManager
        val factory = HomeViewModelFactory(sessionManager)
        homeViewModel = ViewModelProvider(this, factory)[HomeViewModel::class.java]

        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupRecyclerView()
        setupObservers()

        return root
    }

    override fun onResume() {
        super.onResume()
        // refresh homepage data every time user navigates back to homepage
        homeViewModel.refreshHomepageData()
    }

    private fun setupRecyclerView() {
        // Setup ticket boxes adapter
        ticketBoxAdapter = TicketBoxAdapter { ticketBox ->
            handleTicketBoxClick(ticketBox)
        }

        binding.recyclerTicketsBoxes.apply {
            // use span count of 2 to handle 2 items per row
            val gridLayoutManager = GridLayoutManager(requireContext(), 2)
            gridLayoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    val totalItems = ticketBoxAdapter.itemCount
                    if (totalItems == 0) return 1 // default span

                    val itemsInLastRow = totalItems % 2

                    // if we're in the last row and there's only 1 item
                    if (itemsInLastRow == 1 && position == totalItems - 1) {
                        return 2 // single item takes full width (2 spans)
                    }
                    return 1 // normal span size (1 span per item for 2 columns)
                }
            }
            layoutManager = gridLayoutManager
            adapter = ticketBoxAdapter
        }
    }

    private fun setupObservers() {
        homeViewModel.ticketStatistics.observe(viewLifecycleOwner) { statistics ->
            updateTicketBoxes(statistics)
        }

        homeViewModel.groups.observe(viewLifecycleOwner) { groups ->
            updateTicketBoxes(homeViewModel.ticketStatistics.value ?: emptyList(), groups)
        }

        homeViewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            if (!isLoading) {
                binding.errorMessage.visibility = View.GONE
            }
        }
    }

    private fun updateTicketBoxes(statistics: List<ir.rahavardit.ariel.data.model.TicketStatistics>, groups: List<ir.rahavardit.ariel.data.model.EventGroup> = emptyList()) {
        val ticketBoxes = mutableListOf<TicketBox>()

        // Add "All Tickets" box first
        ticketBoxes.add(
            TicketBox(
                type = TicketBoxType.ALL_TICKETS,
                label = "همه تیکت‌ها",
                hasIcon = true
            )
        )

        // Add ticket status boxes
        statistics.forEach { stat ->
            val colors = getColorsForValue(stat.value)
            ticketBoxes.add(
                TicketBox(
                    type = TicketBoxType.TICKET_STATUS,
                    label = stat.label,
                    count = stat.count,
                    value = stat.value,
                    foregroundColor = colors.first
                    // No backgroundColor - will use default surface color like "All Tickets" box
                )
            )
        }

        // Add group boxes
        groups.forEach { group ->
            ticketBoxes.add(
                TicketBox(
                    type = TicketBoxType.GROUP,
                    label = group.name,
                    groupId = group.id
                )
            )
        }

        // Add navigation boxes
        addNavigationBoxes(ticketBoxes)

        if (ticketBoxes.size <= 1) { // Only "All Tickets" box
            binding.errorMessage.text = "اطلاعات در دسترس نیست"
            binding.errorMessage.visibility = View.VISIBLE
        } else {
            binding.errorMessage.visibility = View.GONE
        }

        ticketBoxAdapter.submitList(ticketBoxes)
    }

    private fun getColorsForValue(value: String): Pair<Int, Int> {
        return when (value) {
            "P" -> Pair(
                ContextCompat.getColor(requireContext(), R.color.status_p_fg),
                ContextCompat.getColor(requireContext(), R.color.status_p_bg)
            )
            "I" -> Pair(
                ContextCompat.getColor(requireContext(), R.color.status_i_fg),
                ContextCompat.getColor(requireContext(), R.color.status_i_bg)
            )
            "R" -> Pair(
                ContextCompat.getColor(requireContext(), R.color.status_r_fg),
                ContextCompat.getColor(requireContext(), R.color.status_r_bg)
            )
            "unvisited" -> Pair(
                ContextCompat.getColor(requireContext(), R.color.statistics_unvisited_fg),
                ContextCompat.getColor(requireContext(), R.color.statistics_unvisited_bg)
            )
            else -> Pair(
                ContextCompat.getColor(requireContext(), R.color.statistics_text_color),
                ContextCompat.getColor(requireContext(), R.color.surface)
            )
        }
    }

    private fun addNavigationBoxes(ticketBoxes: MutableList<TicketBox>) {
        val sessionManager = SessionManager(requireContext())
        val user = sessionManager.getUser()

        // Add FAQs box
        ticketBoxes.add(
            TicketBox(
                type = TicketBoxType.FAQS,
                label = "سوالات متداول",
                hasIcon = true,
                backgroundColor = ContextCompat.getColor(requireContext(), R.color.box_faqs_bg)
            )
        )

        // Add Knowledges box
        ticketBoxes.add(
            TicketBox(
                type = TicketBoxType.KNOWLEDGES,
                label = "دانش‌ها",
                hasIcon = true,
                backgroundColor = ContextCompat.getColor(requireContext(), R.color.box_knowledges_bg)
            )
        )

        // Add Events box
        ticketBoxes.add(
            TicketBox(
                type = TicketBoxType.EVENTS,
                label = "رویدادها",
                hasIcon = true,
                backgroundColor = ContextCompat.getColor(requireContext(), R.color.box_events_bg)
            )
        )

        // Add Profile box
        ticketBoxes.add(
            TicketBox(
                type = TicketBoxType.PROFILE,
                label = "پروفایل",
                hasIcon = true,
                backgroundColor = ContextCompat.getColor(requireContext(), R.color.box_profile_bg)
            )
        )

        // Add Users box (only for superusers)
        if (user != null && user.isSuperuser) {
            ticketBoxes.add(
                TicketBox(
                    type = TicketBoxType.USERS,
                    label = "کاربران",
                    hasIcon = true,
                    backgroundColor = ContextCompat.getColor(requireContext(), R.color.box_users_bg)
                )
            )
        }
    }

    private fun handleTicketBoxClick(ticketBox: TicketBox) {
        when (ticketBox.type) {
            TicketBoxType.ALL_TICKETS -> {
                // Navigate to tickets list without any filter
                findNavController().navigate(R.id.nav_tickets)
            }
            TicketBoxType.TICKET_STATUS -> {
                // Navigate to tickets list with status filter
                val bundle = Bundle().apply {
                    putString("status", ticketBox.value)
                }
                findNavController().navigate(R.id.nav_tickets, bundle)
            }
            TicketBoxType.GROUP -> {
                // Navigate to tickets list with group filter
                val bundle = Bundle().apply {
                    putInt("groupId", ticketBox.groupId ?: 0)
                }
                findNavController().navigate(R.id.nav_tickets, bundle)
            }
            TicketBoxType.FAQS -> {
                // Navigate to FAQs page
                findNavController().navigate(R.id.nav_faqs)
            }
            TicketBoxType.KNOWLEDGES -> {
                // Navigate to Knowledges page
                findNavController().navigate(R.id.nav_knowledges)
            }
            TicketBoxType.EVENTS -> {
                // Navigate to Events page
                findNavController().navigate(R.id.nav_events)
            }
            TicketBoxType.PROFILE -> {
                // Navigate to Profile page
                findNavController().navigate(R.id.nav_profile)
            }
            TicketBoxType.USERS -> {
                // Navigate to Users page
                findNavController().navigate(R.id.nav_users)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
