package ir.rahavardit.ariel.ui.home

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.model.TicketBox
import ir.rahavardit.ariel.data.model.TicketBoxType
import ir.rahavardit.ariel.databinding.ItemTicketBoxBinding

class TicketBoxAdapter(
    private val onBoxClick: (TicketBox) -> Unit
) : ListAdapter<TicketBox, TicketBoxAdapter.TicketBoxViewHolder>(TicketBoxDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TicketBoxViewHolder {
        val binding = ItemTicketBoxBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return TicketBoxViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TicketBoxViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class TicketBoxViewHolder(
        private val binding: ItemTicketBoxBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(ticketBox: TicketBox) {
            binding.tvBoxLabel.text = ticketBox.label

            // Handle icon visibility and type
            if (ticketBox.hasIcon) {
                binding.ivBoxIcon.visibility = View.VISIBLE
                val iconRes = when (ticketBox.type) {
                    TicketBoxType.ALL_TICKETS -> R.drawable.ic_menu_tickets
                    TicketBoxType.FAQS -> R.drawable.ic_faqs
                    TicketBoxType.KNOWLEDGES -> R.drawable.ic_knowledges
                    TicketBoxType.EVENTS -> R.drawable.ic_events
                    TicketBoxType.PROFILE -> R.drawable.ic_profile
                    TicketBoxType.USERS -> R.drawable.ic_users
                    else -> R.drawable.ic_menu_tickets
                }
                binding.ivBoxIcon.setImageResource(iconRes)

                // Set icon color based on box type
                val iconColor = when (ticketBox.type) {
                    TicketBoxType.FAQS -> ContextCompat.getColor(binding.root.context, R.color.box_faqs_icon)
                    TicketBoxType.KNOWLEDGES -> ContextCompat.getColor(binding.root.context, R.color.box_knowledges_icon)
                    TicketBoxType.EVENTS -> ContextCompat.getColor(binding.root.context, R.color.box_events_icon)
                    TicketBoxType.PROFILE -> ContextCompat.getColor(binding.root.context, R.color.box_profile_icon)
                    TicketBoxType.USERS -> ContextCompat.getColor(binding.root.context, R.color.box_users_icon)
                    else -> ContextCompat.getColor(binding.root.context, R.color.primary)
                }
                binding.ivBoxIcon.setColorFilter(iconColor)
            } else {
                binding.ivBoxIcon.visibility = View.GONE
            }

            // Handle count visibility and text
            if (ticketBox.count != null) {
                binding.tvBoxCount.visibility = View.VISIBLE
                val persianCount = convertToPersianNumerals(ticketBox.count.toString())
                binding.tvBoxCount.text = persianCount
            } else {
                binding.tvBoxCount.visibility = View.GONE
            }

            // Set colors
            if (ticketBox.foregroundColor != null) {
                // For status boxes, apply the color to both count and label
                binding.tvBoxCount.setTextColor(ticketBox.foregroundColor)
                if (ticketBox.type == TicketBoxType.TICKET_STATUS) {
                    binding.tvBoxLabel.setTextColor(ticketBox.foregroundColor)
                }
            } else {
                binding.tvBoxCount.setTextColor(
                    ContextCompat.getColor(binding.root.context, R.color.primary)
                )
            }

            // Set background color and elevation
            if (ticketBox.backgroundColor != null) {
                binding.root.setCardBackgroundColor(ticketBox.backgroundColor)
            } else {
                binding.root.setCardBackgroundColor(
                    ContextCompat.getColor(binding.root.context, R.color.surface)
                )
            }

            // All boxes now use the same elevation for consistency
            binding.root.cardElevation = 4f

            // Set click listener with animation
            binding.root.setOnClickListener {
                val animation = AnimationUtils.loadAnimation(binding.root.context, R.anim.box_click_scale)
                binding.root.startAnimation(animation)
                onBoxClick(ticketBox)
            }
        }

        private fun convertToPersianNumerals(input: String): String {
            val persianDigits = arrayOf('۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹')
            val englishDigits = arrayOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9')

            var result = input
            for (i in englishDigits.indices) {
                result = result.replace(englishDigits[i], persianDigits[i])
            }
            return result
        }
    }

    private class TicketBoxDiffCallback : DiffUtil.ItemCallback<TicketBox>() {
        override fun areItemsTheSame(oldItem: TicketBox, newItem: TicketBox): Boolean {
            return when {
                oldItem.type != newItem.type -> false
                oldItem.type == TicketBoxType.ALL_TICKETS -> true
                oldItem.type == TicketBoxType.TICKET_STATUS -> oldItem.value == newItem.value
                oldItem.type == TicketBoxType.GROUP -> oldItem.groupId == newItem.groupId
                oldItem.type == TicketBoxType.FAQS -> true
                oldItem.type == TicketBoxType.KNOWLEDGES -> true
                oldItem.type == TicketBoxType.EVENTS -> true
                oldItem.type == TicketBoxType.PROFILE -> true
                oldItem.type == TicketBoxType.USERS -> true
                else -> false
            }
        }

        override fun areContentsTheSame(oldItem: TicketBox, newItem: TicketBox): Boolean {
            return oldItem == newItem
        }
    }
}
