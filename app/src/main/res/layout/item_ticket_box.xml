<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="140dp"
    android:layout_margin="4dp"
    app:cardElevation="4dp"
    app:cardCornerRadius="16dp"
    app:cardBackgroundColor="?attr/colorSurface"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">
    <!-- app:strokeColor="@color/card_border" -->
    <!-- app:strokeWidth="1dp" -->

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="16dp">

        <ImageView
            android:id="@+id/iv_box_icon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginBottom="8dp"
            android:visibility="gone"
            tools:src="@drawable/ic_menu_tickets"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_box_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="13sp"
            android:textStyle="bold"
            android:textColor="?attr/colorOnSurface"
            android:gravity="center"
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_marginBottom="4dp"
            tools:text="همه تیکت‌ها" />

        <TextView
            android:id="@+id/tv_box_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center"
            android:visibility="gone"
            android:textColor="?attr/colorPrimary"
            tools:text="۱۳"
            tools:visibility="visible" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
